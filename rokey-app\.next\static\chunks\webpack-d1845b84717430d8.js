(()=>{"use strict";var e={},t={};function r(a){var n=t[a];if(void 0!==n)return n.exports;var o=t[a]={exports:{}},c=!0;try{e[a].call(o.exports,o,o.exports,r),c=!1}finally{c&&delete t[a]}return o.exports}r.m=e,(()=>{var e=[];r.O=(t,a,n,o)=>{if(a){o=o||0;for(var c=e.length;c>0&&e[c-1][2]>o;c--)e[c]=e[c-1];e[c]=[a,n,o];return}for(var d=1/0,c=0;c<e.length;c++){for(var[a,n,o]=e[c],s=!0,f=0;f<a.length;f++)(!1&o||d>=o)&&Object.keys(r.O).every(e=>r.O[e](a[f]))?a.splice(f--,1):(s=!1,o<d&&(d=o));if(s){e.splice(c--,1);var i=n();void 0!==i&&(t=i)}}return t}})(),r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;r.t=function(a,n){if(1&n&&(a=this(a)),8&n||"object"==typeof a&&a&&(4&n&&a.__esModule||16&n&&"function"==typeof a.then))return a;var o=Object.create(null);r.r(o);var c={};e=e||[null,t({}),t([]),t(t)];for(var d=2&n&&a;"object"==typeof d&&!~e.indexOf(d);d=t(d))Object.getOwnPropertyNames(d).forEach(e=>c[e]=()=>a[e]);return c.default=()=>a,r.d(o,c),o}})(),r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce((t,a)=>(r.f[a](e,t),t),[])),r.u=e=>6308===e?"static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js":563===e?"static/chunks/vendors-ad6a2f20-92808b268818c916.js":2662===e?"static/chunks/vendors-04fef8b0-f66a080f2553b62a.js":8669===e?"static/chunks/vendors-7ec938a2-0338405445251cdc.js":8848===e?"static/chunks/vendors-2ced652b-8bf9d08c4789c20d.js":622===e?"static/chunks/vendors-b49fab05-09e23252f7421d3c.js":2432===e?"static/chunks/vendors-27f02048-f9b9d680c13dc20b.js":408===e?"static/chunks/vendors-fa70753b-62b027da1ccff19b.js":5738===e?"static/chunks/utils-b9a694e34c102292.js":9968===e?"static/chunks/ui-components-b80f15b3-8f8f5e9eb613172f.js":6060===e?"static/chunks/ui-components-3acb5f41-2eee7717f072a5b0.js":"static/chunks/"+(({703:"markdown-cd8c40e0",2548:"markdown-5582deac",3285:"markdown-f75080aa",3466:"playground-heavy",4280:"markdown-b1f8c777",4726:"markdown-f393dd55",5006:"markdown-dbb68ab2",5928:"markdown-c3128679",8960:"markdown-b2d55df5",8961:"markdown-98dda3e8"})[e]||e)+"."+({678:"e5d58f9d442dd47e",703:"cef8ce15008477e7",2548:"88731d1f9c3245f8",3285:"fcb3b50b332effcf",3310:"fe0de0d8efce7dfe",3466:"5b69ab59c5734c20",3613:"e2ec2ddc8da25689",4280:"fef7f0aea284e836",4726:"d72a99c3c3bbc787",5006:"4e0bc25d935d6fa3",5260:"4a3cc312b7749e5a",5721:"26191678a4be675f",5928:"755da21a8b30fb82",7096:"7f1bb11b6d9491bd",7455:"64aa8767398d45d3",7525:"53d09120a34ffe5d",8730:"b1e2fe83d2bd8280",8960:"90162d707f62ebb4",8961:"bad213a2f828f9fe"})[e]+".js",r.miniCssF=e=>{},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="_N_E:";r.l=(a,n,o,c)=>{if(e[a])return void e[a].push(n);if(void 0!==o)for(var d,s,f=document.getElementsByTagName("script"),i=0;i<f.length;i++){var b=f[i];if(b.getAttribute("src")==a||b.getAttribute("data-webpack")==t+o){d=b;break}}d||(s=!0,(d=document.createElement("script")).charset="utf-8",d.timeout=120,r.nc&&d.setAttribute("nonce",r.nc),d.setAttribute("data-webpack",t+o),d.src=r.tu(a)),e[a]=[n];var u=(t,r)=>{d.onerror=d.onload=null,clearTimeout(l);var n=e[a];if(delete e[a],d.parentNode&&d.parentNode.removeChild(d),n&&n.forEach(e=>e(r)),t)return t(r)},l=setTimeout(u.bind(null,void 0,{type:"timeout",target:d}),12e4);d.onerror=u.bind(null,d.onerror),d.onload=u.bind(null,d.onload),s&&document.head.appendChild(d)}})(),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;r.tt=()=>(void 0===e&&(e={createScriptURL:e=>e},"undefined"!=typeof trustedTypes&&trustedTypes.createPolicy&&(e=trustedTypes.createPolicy("nextjs#bundler",e))),e)})(),r.tu=e=>r.tt().createScriptURL(e),r.p="/_next/",(()=>{var e={8068:0,2098:0,7690:0,1911:0};r.f.j=(t,a)=>{var n=r.o(e,t)?e[t]:void 0;if(0!==n)if(n)a.push(n[2]);else if(/^(1911|2098|7690|8068)$/.test(t))e[t]=0;else{var o=new Promise((r,a)=>n=e[t]=[r,a]);a.push(n[2]=o);var c=r.p+r.u(t),d=Error();r.l(c,a=>{if(r.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var o=a&&("load"===a.type?"missing":a.type),c=a&&a.target&&a.target.src;d.message="Loading chunk "+t+" failed.\n("+o+": "+c+")",d.name="ChunkLoadError",d.type=o,d.request=c,n[1](d)}},"chunk-"+t,t)}},r.O.j=t=>0===e[t];var t=(t,a)=>{var n,o,[c,d,s]=a,f=0;if(c.some(t=>0!==e[t])){for(n in d)r.o(d,n)&&(r.m[n]=d[n]);if(s)var i=s(r)}for(t&&t(a);f<c.length;f++)o=c[f],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(i)},a=self.webpackChunk_N_E=self.webpackChunk_N_E||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})()})();