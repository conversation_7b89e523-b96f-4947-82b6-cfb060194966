"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9968],{4654:(e,r,s)=>{s.d(r,{f:()=>i});var a=s(95155);s(12115);var t=s(11485);let i=e=>{let{orchestrationComplete:r,onMaximize:s,isCanvasOpen:i,isCanvasMinimized:o}=e;return(0,a.jsxs)("div",{className:"flex justify-start group mb-16 mt-8 ".concat(i&&!o?"-ml-96":""," ").concat(i&&!o?"ml-8":""),children:[(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,a.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,a.jsx)("div",{className:"".concat(i&&!o?"max-w-[80%]":"max-w-[65%]"," relative"),children:(0,a.jsx)("div",{onClick:s,className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl rounded-bl-lg shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-[1.02] min-w-[320px] ring-2 ring-blue-300/60 hover:ring-blue-300/80 shadow-blue-500/20",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(t.v,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"AI Team Collaboration"}),(0,a.jsx)("p",{className:"text-xs opacity-90",children:r?"Completed - Click to view results":"Multi-Role Orchestration in progress"})]}),(0,a.jsxs)("div",{className:"flex-shrink-0",children:[!r&&(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),r&&(0,a.jsx)(t.B,{className:"w-5 h-5"})]})]})})})]})}},43456:(e,r,s)=>{s.d(r,{A:()=>c});var a=s(95155),t=s(11518),i=s.n(t),o=s(12115),l=s(2299),n=s(64134);let d={initializing:{icon:l.P,text:"Initializing",description:"Starting up systems",bgColor:"bg-gradient-to-r from-slate-50 to-gray-50",iconColor:"text-slate-600",borderColor:"border-slate-200/60",glowColor:"shadow-slate-200/50",gradientFrom:"from-slate-400",gradientTo:"to-gray-400",duration:200},analyzing:{icon:l.$p,text:"Analyzing",description:"Understanding your request",bgColor:"bg-gradient-to-r from-cyan-50 to-blue-50",iconColor:"text-cyan-600",borderColor:"border-cyan-200/60",glowColor:"shadow-cyan-200/50",gradientFrom:"from-cyan-400",gradientTo:"to-blue-400",duration:300},routing:{icon:l.EF,text:"Smart routing",description:"Finding optimal path",bgColor:"bg-gradient-to-r from-indigo-50 to-purple-50",iconColor:"text-indigo-600",borderColor:"border-indigo-200/60",glowColor:"shadow-indigo-200/50",gradientFrom:"from-indigo-400",gradientTo:"to-purple-400",duration:400},complexity_analysis:{icon:l.XL,text:"Analyzing complexity",description:"Evaluating request depth",bgColor:"bg-gradient-to-r from-amber-50 to-yellow-50",iconColor:"text-amber-600",borderColor:"border-amber-200/60",glowColor:"shadow-amber-200/50",gradientFrom:"from-amber-400",gradientTo:"to-yellow-400",duration:500},role_classification:{icon:l.Gg,text:"Assembling specialists",description:"Building expert team",bgColor:"bg-gradient-to-r from-violet-50 to-purple-50",iconColor:"text-violet-600",borderColor:"border-violet-200/60",glowColor:"shadow-violet-200/50",gradientFrom:"from-violet-400",gradientTo:"to-purple-400",duration:600},preparing:{icon:l.DQ,text:"Preparing",description:"Setting up processing",bgColor:"bg-gradient-to-r from-orange-50 to-amber-50",iconColor:"text-orange-600",borderColor:"border-orange-200/60",glowColor:"shadow-orange-200/50",gradientFrom:"from-orange-400",gradientTo:"to-amber-400",duration:300},connecting:{icon:l.nr,text:"Connecting",description:"Establishing AI link",bgColor:"bg-gradient-to-r from-rose-50 to-pink-50",iconColor:"text-rose-600",borderColor:"border-rose-200/60",glowColor:"shadow-rose-200/50",gradientFrom:"from-rose-400",gradientTo:"to-pink-400",duration:400},generating:{icon:l.Y3,text:"Thinking deeply",description:"AI processing in progress",bgColor:"bg-gradient-to-r from-emerald-50 to-teal-50",iconColor:"text-emerald-600",borderColor:"border-emerald-200/60",glowColor:"shadow-emerald-200/50",gradientFrom:"from-emerald-400",gradientTo:"to-teal-400",duration:800},typing:{icon:l.R2,text:"Streaming response",description:"Delivering your answer",bgColor:"bg-gradient-to-r from-green-50 to-emerald-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-emerald-400"},finalizing:{icon:l.BZ,text:"Finalizing",description:"Adding finishing touches",bgColor:"bg-gradient-to-r from-teal-50 to-cyan-50",iconColor:"text-teal-600",borderColor:"border-teal-200/60",glowColor:"shadow-teal-200/50",gradientFrom:"from-teal-400",gradientTo:"to-cyan-400",duration:200},complete:{icon:l.Zu,text:"Complete",description:"Response delivered",bgColor:"bg-gradient-to-r from-green-50 to-lime-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-lime-400",duration:100}};function c(e){let{currentStage:r,isStreaming:s=!1,className:t="",onStageChange:c,orchestrationStatus:m}=e,[g,x]=(0,o.useState)(r),[h,u]=(0,o.useState)(!1),[p,b]=(0,o.useState)(0),[f,j]=(0,o.useState)(""),[y,w]=(0,o.useState)(1),v=[{bgColor:"bg-gradient-to-r from-blue-50 to-indigo-50",iconColor:"text-blue-600",borderColor:"border-blue-200/60",glowColor:"shadow-blue-200/50",gradientFrom:"from-blue-400",gradientTo:"to-indigo-400"},{bgColor:"bg-gradient-to-r from-purple-50 to-violet-50",iconColor:"text-purple-600",borderColor:"border-purple-200/60",glowColor:"shadow-purple-200/50",gradientFrom:"from-purple-400",gradientTo:"to-violet-400"},{bgColor:"bg-gradient-to-r from-indigo-50 to-blue-50",iconColor:"text-indigo-600",borderColor:"border-indigo-200/60",glowColor:"shadow-indigo-200/50",gradientFrom:"from-indigo-400",gradientTo:"to-blue-400"},{bgColor:"bg-gradient-to-r from-cyan-50 to-teal-50",iconColor:"text-cyan-600",borderColor:"border-cyan-200/60",glowColor:"shadow-cyan-200/50",gradientFrom:"from-cyan-400",gradientTo:"to-teal-400"},{bgColor:"bg-gradient-to-r from-teal-50 to-emerald-50",iconColor:"text-teal-600",borderColor:"border-teal-200/60",glowColor:"shadow-teal-200/50",gradientFrom:"from-teal-400",gradientTo:"to-emerald-400"},{bgColor:"bg-gradient-to-r from-green-50 to-lime-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-lime-400"},{bgColor:"bg-gradient-to-r from-yellow-50 to-amber-50",iconColor:"text-yellow-600",borderColor:"border-yellow-200/60",glowColor:"shadow-yellow-200/50",gradientFrom:"from-yellow-400",gradientTo:"to-amber-400"},{bgColor:"bg-gradient-to-r from-orange-50 to-red-50",iconColor:"text-orange-600",borderColor:"border-orange-200/60",glowColor:"shadow-orange-200/50",gradientFrom:"from-orange-400",gradientTo:"to-red-400"},{bgColor:"bg-gradient-to-r from-rose-50 to-pink-50",iconColor:"text-rose-600",borderColor:"border-rose-200/60",glowColor:"shadow-rose-200/50",gradientFrom:"from-rose-400",gradientTo:"to-pink-400"},{bgColor:"bg-gradient-to-r from-emerald-50 to-teal-50",iconColor:"text-emerald-600",borderColor:"border-emerald-200/60",glowColor:"shadow-emerald-200/50",gradientFrom:"from-emerald-400",gradientTo:"to-teal-400"}],N=v[p%v.length],C=m?{...d[g],...N,icon:m.includes("\uD83D\uDD0D")||m.includes("detected")?l.$p:m.includes("✅")||m.includes("complete")?l.C1:m.includes("\uD83C\uDFAF")||m.includes("Selected")?n.g:m.includes("\uD83C\uDFD7️")||m.includes("workflow")?l.DP:m.includes("\uD83E\uDD16")||m.includes("agent")?l.K6:m.includes("\uD83D\uDC51")||m.includes("supervisor")?l.Gg:m.includes("\uD83D\uDCCB")||m.includes("Planning")?l.Pp:m.includes("\uD83D\uDE80")||m.includes("starting")?l.P:m.includes("\uD83D\uDD04")||m.includes("synthesizing")?l.EF:d[g].icon}:d[g],k=C.icon;return(0,o.useEffect)(()=>{r!==g&&(u(!0),setTimeout(()=>{x(r),u(!1),null==c||c(r)},200))},[r,g,c]),(0,o.useEffect)(()=>{m&&m!==f&&(j(m),b(e=>e+1),u(!0),w(2.5),setTimeout(()=>{u(!1)},300),setTimeout(()=>{w(1)},1e3))},[m,f]),(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 "+"flex justify-start ".concat(t),children:[(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0",children:[(0,a.jsx)("div",{style:{animation:"spin ".concat(1.2/y,"s linear infinite"),borderTopColor:C.iconColor.replace("text-",""),filter:"drop-shadow(0 0 6px rgba(59, 130, 246, 0.4))"},className:"jsx-f56d70faa8a01b64 absolute -inset-2 w-10 h-10 rounded-full border-[3px] border-t-blue-500 border-r-transparent border-b-transparent border-l-transparent"}),(0,a.jsx)("div",{style:{animation:"spin ".concat(1.8/y,"s linear infinite reverse"),borderTopColor:C.iconColor.replace("text-","").replace("600","400"),opacity:.7},className:"jsx-f56d70faa8a01b64 absolute -inset-1.5 w-9 h-9 rounded-full border-[2px] border-t-purple-400 border-r-transparent border-b-transparent border-l-transparent"}),(0,a.jsx)("div",{style:{animation:"spin ".concat(.8/y,"s linear infinite"),borderTopColor:C.iconColor.replace("text-","").replace("600","300"),opacity:.5},className:"jsx-f56d70faa8a01b64 absolute -inset-1 w-8 h-8 rounded-full border-[1px] border-t-cyan-300 border-r-transparent border-b-transparent border-l-transparent"}),(0,a.jsx)("div",{style:{borderColor:C.iconColor.replace("text-","").replace("600","200"),opacity:.3,animation:"pulse 2s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 absolute -inset-0.5 w-7 h-7 rounded-full border animate-pulse"}),(0,a.jsx)("div",{style:{boxShadow:"0 0 12px ".concat(C.iconColor.replace("text-",""),"40, 0 0 24px ").concat(C.iconColor.replace("text-",""),"20")},className:"jsx-f56d70faa8a01b64 "+"relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 ".concat(C.bgColor," border-2 ").concat(C.borderColor," shadow-lg backdrop-blur-sm"),children:(0,a.jsx)(k,{className:"jsx-f56d70faa8a01b64 "+"w-3.5 h-3.5 transition-all duration-500 ".concat(C.iconColor," ").concat(h?"scale-125 rotate-12":"scale-100"," drop-shadow-lg")})})]}),(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 "+"max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 ".concat(C.bgColor," ").concat(C.borderColor," border ").concat(C.glowColor," shadow-sm backdrop-blur-sm"),children:[(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 flex items-center space-x-1.5",children:(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 transition-all duration-500",children:[(0,a.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"text-xs font-semibold transition-colors duration-500 ".concat(C.iconColor," tracking-wide"),children:m||C.text}),s&&"typing"===g&&!m&&(0,a.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"ml-1.5 text-[10px] opacity-80 ".concat(C.iconColor," font-medium"),children:"• Live"}),m&&(0,a.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"ml-1.5 text-[10px] opacity-80 ".concat(C.iconColor," font-medium"),children:"• Orchestrating"})]})}),("generating"===g||"typing"===g)&&(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 mt-2",children:(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20",children:(0,a.jsx)("div",{style:{width:"typing"===g?"100%":"60%",animation:"typing"===g?"progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite":"progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 "+"h-full rounded-full transition-all duration-1000 bg-gradient-to-r ".concat(C.gradientFrom," ").concat(C.gradientTo," relative overflow-hidden"),children:(0,a.jsx)("div",{style:{animation:"progressShine 2s linear infinite",transform:"skewX(-20deg)"},className:"jsx-f56d70faa8a01b64 absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"})})})})]}),(0,a.jsx)(i(),{id:"f56d70faa8a01b64",children:"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}"})]})}},52399:(e,r,s)=>{s.d(r,{c:()=>o});var a=s(95155),t=s(12115),i=s(60875);let o=e=>{var r,s;let{message:o}=e,l=e=>{switch(e){case"assignment":return(0,a.jsx)(i.fl,{className:"w-3 h-3 text-blue-500"});case"completion":return(0,a.jsx)(i.C1,{className:"w-3 h-3 text-green-500"});case"handoff":return(0,a.jsx)(i.fl,{className:"w-3 h-3 text-purple-500"});default:return null}},n="moderator"===o.sender,d=(e=>{if(!e)return"from-blue-500 to-blue-600";let r=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return r[e.split("").reduce((e,r)=>e+r.charCodeAt(0),0)%r.length]})(o.roleId);return(0,a.jsx)("div",{className:"flex ".concat("justify-start"," mb-4"),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ".concat(d," flex items-center justify-center text-white shadow-sm"),children:(r=o.sender,o.roleId,"moderator"===r?(0,a.jsx)(i.BZ,{className:"w-4 h-4"}):(0,a.jsx)(i.YE,{className:"w-4 h-4"}))}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-semibold ".concat(n?"text-blue-700":"text-gray-700"),children:o.senderName}),l(o.type)&&(0,a.jsx)("div",{className:"flex items-center",children:l(o.type)}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:o.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),(0,a.jsx)("div",{className:"inline-block px-4 py-3 rounded-2xl shadow-sm ".concat(n?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"," ").concat("completion"===o.type?"border-green-200 bg-green-50":"assignment"===o.type?"border-blue-200 bg-blue-50":"handoff"===o.type?"border-purple-200 bg-purple-50":""),children:(0,a.jsx)("div",{className:"text-sm leading-relaxed ".concat(n?"text-blue-900":"text-gray-800"," ").concat("completion"===o.type?"text-green-900":"assignment"===o.type?"text-blue-900":"handoff"===o.type?"text-purple-900":""),children:o.content.split("\n").map((e,r)=>(0,a.jsxs)(t.Fragment,{children:[e,r<o.content.split("\n").length-1&&(0,a.jsx)("br",{})]},r))})}),"message"!==o.type&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat("assignment"===o.type?"bg-blue-100 text-blue-800":"completion"===o.type?"bg-green-100 text-green-800":"handoff"===o.type?"bg-purple-100 text-purple-800":"clarification"===o.type?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:["assignment"===o.type&&"\uD83D\uDCCB Task Assignment","completion"===o.type&&"✅ Task Complete","handoff"===o.type&&"\uD83D\uDD04 Handoff","clarification"===o.type&&"❓ Clarification"]})})]})]})})}},52469:(e,r,s)=>{s.d(r,{default:()=>o});var a=s(12115),t=s(35695);let i=["/features","/pricing","/about","/auth/signin","/auth/signup"];function o(){let e=(0,t.useRouter)();return(0,a.useEffect)(()=>{let r=()=>{i.forEach(r=>{e.prefetch(r)})};"requestIdleCallback"in window?window.requestIdleCallback(r,{timeout:2e3}):setTimeout(r,100)},[e]),null}},73360:(e,r,s)=>{s.d(r,{A:()=>o});var a=s(95155),t=s(12115),i=s(58637);function o(e){let{configId:r,onDocumentUploaded:s}=e,[o,l]=(0,t.useState)([]),[n,d]=(0,t.useState)(!1),[c,m]=(0,t.useState)(!1),[g,x]=(0,t.useState)(0),[h,u]=(0,t.useState)(!1),[p,b]=(0,t.useState)(null),[f,j]=(0,t.useState)(null),y=(0,t.useRef)(null),w=(0,t.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,s=arguments.length>1?arguments[1]:void 0;if(r){0===e&&m(!0);try{let a=await fetch("/api/documents/list?configId=".concat(r));if(a.ok){let r=(await a.json()).documents||[];if(s&&e<3&&!r.find(e=>e.id===s))return void setTimeout(()=>{w(e+1,s)},(e+1)*500);l(e=>{let s=new Set(e.map(e=>e.id)),a=r.filter(e=>!s.has(e.id));return[...e.map(e=>r.find(r=>r.id===e.id)||e),...a].sort((e,r)=>new Date(r.created_at).getTime()-new Date(e.created_at).getTime())})}}catch(r){e<2&&setTimeout(()=>{w(e+1,s)},1e3)}finally{(0===e||s)&&m(!1)}}},[r]);t.useEffect(()=>{w()},[w]);let v=async e=>{if(!r)return void b("Please select an API configuration first");let a=e[0];if(a){if(!["application/pdf","text/plain","text/markdown"].includes(a.type))return void b("Please upload PDF, TXT, or MD files only");if(a.size>0xa00000)return void b("File size must be less than 10MB");d(!0),b(null),j(null),x(0);try{let e=new FormData;e.append("file",a),e.append("configId",r);let t=setInterval(()=>{x(e=>Math.min(e+10,90))},200),i=await fetch("/api/documents/upload",{method:"POST",body:e});if(clearInterval(t),x(100),!i.ok){let e=await i.json();throw Error(e.error||"Upload failed")}let o=await i.json();j("✨ ".concat(a.name," uploaded successfully! Processing ").concat(o.document.chunks_total," chunks."));let n={id:o.document.id,filename:o.document.filename||a.name,file_type:a.type,file_size:a.size,status:o.document.status||"processing",chunks_count:o.document.chunks_processed||0,created_at:new Date().toISOString()};l(e=>e.find(e=>e.id===n.id)?e.map(e=>e.id===n.id?n:e):[n,...e]),setTimeout(async()=>{await w(0,o.document.id)},200),null==s||s()}catch(e){b("Upload failed: ".concat(e.message)),setTimeout(()=>b(null),8e3)}finally{d(!1),x(0),y.current&&(y.current.value=""),f&&setTimeout(()=>j(null),5e3)}}},N=(0,t.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?u(!0):"dragleave"===e.type&&u(!1)},[]),C=(0,t.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),u(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&v(e.dataTransfer.files)},[r]),k=async e=>{if(confirm("Are you sure you want to delete this document?")){l(r=>r.filter(r=>r.id!==e));try{if(!(await fetch("/api/documents/".concat(e),{method:"DELETE"})).ok)throw l(o),Error("Failed to delete document");j("Document deleted successfully"),await w(),setTimeout(()=>j(null),3e3)}catch(e){l(o),b("Delete failed: ".concat(e.message)),setTimeout(()=>b(null),8e3)}}},T=e=>{if(0===e)return"0 Bytes";let r=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,r)).toFixed(2))+" "+["Bytes","KB","MB","GB"][r]},S=e=>e.includes("pdf")?(0,a.jsx)(i.iU,{className:"w-5 h-5 text-red-500"}):e.includes("word")?(0,a.jsx)(i.iU,{className:"w-5 h-5 text-blue-500"}):(0,a.jsx)(i.ZH,{className:"w-5 h-5 text-gray-500"});return(0,a.jsxs)("div",{className:"space-y-6",children:[p&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.RI,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("p",{className:"text-red-800 text-sm font-medium",children:p})]})}),f&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.rA,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("p",{className:"text-green-800 text-sm font-medium",children:f})]})}),(0,a.jsxs)("div",{className:"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform ".concat(h?"border-orange-400 bg-orange-50 scale-105 shadow-lg":"border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md"," ").concat(r?"cursor-pointer":"opacity-50 cursor-not-allowed"),onDragEnter:N,onDragLeave:N,onDragOver:N,onDrop:C,onClick:()=>{var e;return r&&(null==(e=y.current)?void 0:e.click())},children:[(0,a.jsx)("input",{ref:y,type:"file",className:"hidden",accept:".pdf,.txt,.md",onChange:e=>{e.target.files&&e.target.files[0]&&v(e.target.files)},disabled:!r||n}),n?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(i.kr,{className:"w-12 h-12 text-orange-500 mx-auto animate-spin"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-900",children:"Processing Document..."}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(g,"%")}})}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[g,"% complete"]})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(i._O,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-900",children:r?"Upload Knowledge Documents":"Select a configuration first"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Drag and drop files here, or click to browse"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Supports PDF, TXT, MD files up to 10MB"})]})]})]}),o.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Uploaded Documents"}),c&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(i.kr,{className:"w-4 h-4 animate-spin"}),(0,a.jsx)("span",{children:"Refreshing..."})]})]}),(0,a.jsx)("div",{className:"grid gap-4",children:o.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[S(e.file_type),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.filename}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[T(e.file_size)," • ",e.chunks_count," chunks"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:["completed"===e.status&&(0,a.jsx)(i.rA,{className:"w-5 h-5 text-green-500"}),"processing"===e.status&&(0,a.jsx)(i.kr,{className:"w-5 h-5 text-orange-500 animate-spin"}),"failed"===e.status&&(0,a.jsx)(i.RI,{className:"w-5 h-5 text-red-500"}),(0,a.jsx)("span",{className:"text-sm font-medium ".concat("completed"===e.status?"text-green-600":"processing"===e.status?"text-orange-600":"text-red-600"),children:"completed"===e.status?"Ready":"processing"===e.status?"Processing":"Failed"})]}),(0,a.jsx)("button",{onClick:()=>k(e.id),className:"p-1 text-gray-400 hover:text-red-500 transition-colors",title:"Delete document",children:(0,a.jsx)(i.X,{className:"w-4 h-4"})})]})]},e.id))})]})]})}},79112:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(95155),t=s(12115);let i=(0,t.lazy)(()=>Promise.all([s.e(5006),s.e(5928),s.e(4726),s.e(4280),s.e(2548),s.e(8960),s.e(8961),s.e(703),s.e(3285),s.e(9968),s.e(6060),s.e(3613),s.e(5260),s.e(7525),s.e(3310),s.e(7096),s.e(7455),s.e(678),s.e(8730)]).then(s.bind(s,90882))),o=()=>(0,a.jsxs)("div",{className:"space-y-2 animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]});function l(e){let{content:r,className:s=""}=e;return(0,a.jsx)(t.Suspense,{fallback:(0,a.jsx)(o,{}),children:(0,a.jsx)(i,{content:r,className:s})})}},79958:(e,r,s)=>{s.d(r,{A:()=>t,_:()=>i});var a=s(95155);function t(){return(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-64 animate-pulse"})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-12 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse"})]})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 animate-pulse"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,a.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-lg animate-pulse"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]})]}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-12 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-14 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-18 animate-pulse"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-28 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:[1,2,3].map(e=>(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded-full w-20 animate-pulse"},e))})]})]},e))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-36 animate-pulse"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-28 animate-pulse"})]}),(0,a.jsx)("div",{className:"space-y-3",children:[1,2].map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-48 animate-pulse"})]}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]},e))})]})]})}function i(){return(0,a.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-56 animate-pulse"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(e=>(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]})},e))})]})}s(12115)},83434:(e,r,s)=>{s.d(r,{default:()=>u});var a=s(95155),t=s(35695),i=s(22261),o=s(99323),l=s(12115),n=s(95494),d=s(95060),c=s(78817),m=s(69903),g=s(42126);function x(e){let{children:r}=e,{isCollapsed:s,isHovered:t,collapseSidebar:c}=(0,i.c)(),{isNavigating:x,targetRoute:h,isPageCached:u}=(0,o.bu)()||{isNavigating:!1,targetRoute:null,isPageCached:()=>!1},[p,b]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=()=>{b(window.innerWidth>=1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let f=p?!s||t?256:64:0;return(0,g.v)({maxConcurrent:2,idleTimeout:1500,backgroundDelay:3e3}),(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)("div",{className:"hidden lg:block fixed left-0 top-0 h-full z-40",children:(0,a.jsx)(d.A,{})}),(0,a.jsxs)("div",{className:"lg:hidden fixed inset-0 z-50 ".concat(s?"pointer-events-none":""),children:[(0,a.jsx)("div",{onClick:c,className:"absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer ".concat(s?"opacity-0":"opacity-50")}),(0,a.jsx)("div",{className:"absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out ".concat(s?"-translate-x-full":"translate-x-0"),children:(0,a.jsx)(d.A,{})})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden min-w-0 transition-all duration-200 ease-out",style:{marginLeft:"".concat(f,"px")},children:[(0,a.jsx)("div",{className:"fixed top-0 right-0 z-30 transition-all duration-200 ease-out",style:{left:"".concat(f,"px")},children:(0,a.jsx)(n.A,{})}),(0,a.jsx)("main",{className:"flex-1 overflow-y-auto content-area mt-16",children:(0,a.jsx)("div",{className:"p-4 sm:p-6 lg:p-8 w-full ".concat(p&&(!s||t)?"max-w-7xl mx-auto":p?"max-w-none px-8":"max-w-7xl mx-auto"),children:(0,a.jsx)("div",{className:"page-transition",children:x&&h?(0,a.jsx)(m.A,{targetRoute:h,children:r}):r})})})]})]})}function h(e){let{children:r}=e;return(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)(c.A,{targetRoute:null}),children:(0,a.jsx)(x,{children:r})})}function u(e){let{children:r}=e,s=(0,t.usePathname)();return"/"===s||s.startsWith("/pricing")||s.startsWith("/features")||s.startsWith("/about")||s.startsWith("/routing-strategies")||s.startsWith("/contact")||s.startsWith("/auth/")?(0,a.jsx)(a.Fragment,{children:r}):(0,a.jsx)(i.G,{children:(0,a.jsx)(o.i9,{children:(0,a.jsx)(h,{children:r})})})}},90882:(e,r,s)=>{s.r(r),s.d(r,{default:()=>d});var a=s(95155),t=s(28831),i=s(70765),o=s(18730),l=s(15478),n=s(95803);function d(e){let{content:r,className:s=""}=e;return(0,a.jsx)("div",{className:"markdown-content ".concat(s),children:(0,a.jsx)(t.Ay,{remarkPlugins:[i.A],components:{h1:e=>{let{children:r}=e;return(0,a.jsx)("h1",{className:"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900",children:r})},h2:e=>{let{children:r}=e;return(0,a.jsx)("h2",{className:"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900",children:r})},h3:e=>{let{children:r}=e;return(0,a.jsx)("h3",{className:"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900",children:r})},h4:e=>{let{children:r}=e;return(0,a.jsx)("h4",{className:"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900",children:r})},p:e=>{let{children:r}=e;return(0,a.jsx)("p",{className:"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words",children:r})},strong:e=>{let{children:r}=e;return(0,a.jsx)("strong",{className:"font-bold text-gray-900",children:r})},em:e=>{let{children:r}=e;return(0,a.jsx)("em",{className:"italic text-gray-900",children:r})},ul:e=>{let{children:r}=e;return(0,a.jsx)("ul",{className:"list-disc list-inside mb-3 space-y-1 text-gray-900",children:r})},ol:e=>{let{children:r}=e;return(0,a.jsx)("ol",{className:"list-decimal list-inside mb-3 space-y-1 text-gray-900",children:r})},li:e=>{let{children:r}=e;return(0,a.jsx)("li",{className:"leading-relaxed text-gray-900",children:r})},code:e=>{let{node:r,inline:s,className:t,children:i,...d}=e,c=/language-(\w+)/.exec(t||""),m=c?c[1]:"",g=String(i).replace(/\n$/,"");if(!s)if(m)return(0,a.jsxs)("div",{className:"my-3 rounded-lg overflow-hidden relative group",children:[(0,a.jsx)("div",{className:"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)(n.A,{text:g,variant:"code",size:"sm",title:"Copy code",className:"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"})}),(0,a.jsx)(o.M,{style:l.bM,language:m,PreTag:"div",className:"text-sm",...d,children:g})]});else return(0,a.jsxs)("div",{className:"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100",children:[(0,a.jsx)("div",{className:"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)(n.A,{text:g,variant:"code",size:"sm",title:"Copy code",className:"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"})}),(0,a.jsx)("pre",{className:"p-4 text-sm font-mono overflow-x-auto",children:(0,a.jsx)("code",{children:g})})]});return(0,a.jsx)("code",{className:"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono",...d,children:i})},blockquote:e=>{let{children:r}=e;return(0,a.jsx)("blockquote",{className:"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700",children:r})},a:e=>{let{children:r,href:s}=e;return(0,a.jsx)("a",{href:s,target:"_blank",rel:"noopener noreferrer",className:"text-orange-600 hover:text-orange-700 underline transition-colors duration-200",children:r})},table:e=>{let{children:r}=e;return(0,a.jsx)("div",{className:"overflow-x-auto my-3",children:(0,a.jsx)("table",{className:"min-w-full border border-gray-200 rounded-lg",children:r})})},thead:e=>{let{children:r}=e;return(0,a.jsx)("thead",{className:"bg-gray-50",children:r})},tbody:e=>{let{children:r}=e;return(0,a.jsx)("tbody",{className:"divide-y divide-gray-200 bg-white",children:r})},tr:e=>{let{children:r}=e;return(0,a.jsx)("tr",{className:"hover:bg-gray-50",children:r})},th:e=>{let{children:r}=e;return(0,a.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200",children:r})},td:e=>{let{children:r}=e;return(0,a.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900 border-b border-gray-200",children:r})},hr:()=>(0,a.jsx)("hr",{className:"my-4 border-gray-200"})},children:r})})}},95565:(e,r,s)=>{s.d(r,{AnalyticsSkeleton:()=>m,ConfigSelectorSkeleton:()=>o,MessageSkeleton:()=>i,MyModelsSkeleton:()=>n,O2:()=>l,RoutingSetupSkeleton:()=>d,vD:()=>c});var a=s(95155);s(11518),s(12115);let t=e=>{let{className:r="",variant:s="text",width:t="100%",height:i="1rem",lines:o=1}=e,l="animate-pulse bg-gray-200 rounded",n=()=>{switch(s){case"circular":return"rounded-full";case"rectangular":return"rounded-lg";default:return"rounded"}},d={width:"number"==typeof t?"".concat(t,"px"):t,height:"number"==typeof i?"".concat(i,"px"):i};return o>1?(0,a.jsx)("div",{className:"space-y-2 ".concat(r),children:Array.from({length:o}).map((e,r)=>(0,a.jsx)("div",{className:"".concat(l," ").concat(n()),style:{...d,width:r===o-1?"75%":d.width}},r))}):(0,a.jsx)("div",{className:"".concat(l," ").concat(n()," ").concat(r),style:d})},i=()=>(0,a.jsx)("div",{className:"space-y-6 py-8",children:Array.from({length:3}).map((e,r)=>(0,a.jsx)("div",{className:"flex ".concat(r%2==0?"justify-end":"justify-start"),children:(0,a.jsx)("div",{className:"max-w-3xl p-4 rounded-2xl ".concat(r%2==0?"bg-orange-50":"bg-white border border-gray-200"),children:(0,a.jsx)(t,{lines:3,height:"1rem"})})},r))}),o=()=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(t,{variant:"circular",width:32,height:32}),(0,a.jsx)(t,{width:"8rem",height:"1.5rem"})]}),l=()=>(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(t,{height:"2.5rem",width:"12rem",className:"mb-2"}),(0,a.jsx)(t,{height:"1.25rem",width:"20rem"})]}),(0,a.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"8rem"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,r)=>(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(t,{variant:"circular",width:40,height:40}),(0,a.jsx)(t,{height:"1rem",width:"3rem"})]}),(0,a.jsx)(t,{height:"2rem",width:"4rem",className:"mb-2"}),(0,a.jsx)(t,{height:"0.875rem",width:"6rem"})]},r))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)(t,{height:"1.5rem",width:"8rem",className:"mb-4"}),(0,a.jsx)(t,{variant:"rectangular",height:"20rem"})]}),(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)(t,{height:"1.5rem",width:"10rem",className:"mb-4"}),(0,a.jsx)(t,{variant:"rectangular",height:"20rem"})]})]})]}),n=()=>(0,a.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(t,{height:"2.5rem",width:"10rem",className:"mb-2"}),(0,a.jsx)(t,{height:"1.25rem",width:"18rem"})]}),(0,a.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"10rem"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,r)=>(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(t,{height:"1.5rem",width:"8rem",className:"mb-2"}),(0,a.jsx)(t,{height:"1rem",width:"12rem"})]}),(0,a.jsx)(t,{variant:"circular",width:32,height:32})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(t,{height:"0.875rem",width:"4rem"}),(0,a.jsx)(t,{height:"0.875rem",width:"2rem"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(t,{height:"0.875rem",width:"5rem"}),(0,a.jsx)(t,{height:"0.875rem",width:"3rem"})]})]})]},r))})]}),d=()=>(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(t,{height:"3rem",width:"16rem",className:"mx-auto mb-4"}),(0,a.jsx)(t,{height:"1.25rem",width:"24rem",className:"mx-auto"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:Array.from({length:4}).map((e,r)=>(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(t,{variant:"circular",width:48,height:48,className:"mr-4"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(t,{height:"1.5rem",width:"10rem",className:"mb-2"}),(0,a.jsx)(t,{height:"1rem",width:"8rem"})]})]}),(0,a.jsx)(t,{lines:3,height:"0.875rem"})]},r))})]}),c=()=>(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(t,{height:"2.5rem",width:"8rem",className:"mb-2"}),(0,a.jsx)(t,{height:"1.25rem",width:"16rem"})]}),(0,a.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"12rem"})]}),(0,a.jsx)("div",{className:"card p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(t,{variant:"circular",width:64,height:64,className:"mx-auto mb-4"}),(0,a.jsx)(t,{height:"1.5rem",width:"12rem",className:"mx-auto mb-2"}),(0,a.jsx)(t,{height:"1rem",width:"20rem",className:"mx-auto"})]})}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)(t,{height:"1.5rem",width:"10rem"})}),(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:Array.from({length:3}).map((e,r)=>(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(t,{height:"1.25rem",width:"12rem",className:"mb-2"}),(0,a.jsx)(t,{height:"1rem",width:"8rem"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(t,{height:"1.5rem",width:"4rem"}),(0,a.jsx)(t,{variant:"circular",width:32,height:32})]})]})},r))})]})]}),m=()=>(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(t,{height:"2.5rem",width:"9rem",className:"mb-2"}),(0,a.jsx)(t,{height:"1.25rem",width:"18rem"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"8rem"}),(0,a.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"6rem"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,r)=>(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)(t,{height:"1.25rem",width:"6rem",className:"mb-4"}),(0,a.jsx)(t,{height:"2.5rem",width:"5rem",className:"mb-2"}),(0,a.jsx)(t,{height:"1rem",width:"8rem"})]},r))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)(t,{height:"1.5rem",width:"12rem",className:"mb-6"}),(0,a.jsx)(t,{variant:"rectangular",height:"24rem"})]}),(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)(t,{height:"1.5rem",width:"10rem",className:"mb-6"}),(0,a.jsx)(t,{variant:"rectangular",height:"24rem"})]})]})]})},95803:(e,r,s)=>{s.d(r,{A:()=>o});var a=s(95155),t=s(12115),i=s(76032);function o(e){let{text:r,className:s="",size:o="sm",variant:l="default",title:n="Copy to clipboard"}=e,[d,c]=(0,t.useState)(!1),m=async()=>{try{await navigator.clipboard.writeText(r),c(!0),setTimeout(()=>c(!1),2e3)}catch(s){let e=document.createElement("textarea");e.value=r,document.body.appendChild(e),e.focus(),e.select();try{document.execCommand("copy"),c(!0),setTimeout(()=>c(!1),2e3)}catch(e){}document.body.removeChild(e)}},g={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"};return(0,a.jsx)("button",{onClick:m,className:"\n        ".concat({sm:"p-1.5",md:"p-2",lg:"p-2.5"}[o],"\n        ").concat({default:"text-gray-500 hover:text-gray-700 hover:bg-gray-100/80",code:"text-gray-300 hover:text-white hover:bg-gray-600/80",message:"text-gray-500 hover:text-gray-700 hover:bg-white/20"}[l],"\n        rounded transition-all duration-200 cursor-pointer\n        ").concat(d?"text-green-600":"","\n        ").concat(s,"\n      "),title:d?"Copied!":n,children:d?(0,a.jsx)(i.S,{className:"".concat(g[o]," stroke-2")}):(0,a.jsx)(i.X,{className:"".concat(g[o]," stroke-2")})})}},99030:(e,r,s)=>{s.d(r,{default:()=>i});var a=s(12115),t=s(35695);function i(){let e=(0,t.usePathname)();return(0,a.useEffect)(()=>{"undefined"!=typeof document&&(document.title=(e=>{switch(e){case"/dashboard":return"Dashboard - RouKey";case"/playground":return"Playground - RouKey";case"/my-models":return"My Models - RouKey";case"/routing-setup":return"Routing Setup - RouKey";case"/logs":return"Logs - RouKey";case"/training":return"Prompt Engineering - RouKey";case"/analytics":return"Analytics - RouKey";case"/add-keys":return"Add Keys - RouKey";case"/features":return"Features - RouKey";case"/routing-strategies":return"Routing Strategies - RouKey";case"/contact":return"Contact - RouKey";case"/about":return"About - RouKey";case"/pricing":return"Pricing - RouKey";default:return"RouKey - AI Gateway"}})(e))},[e]),null}}}]);