{"/api/activity/route": "/api/activity", "/api/admin/populate-cost-tiers/route": "/api/admin/populate-cost-tiers", "/api/analytics/summary/route": "/api/analytics/summary", "/api/cache/invalidate/route": "/api/cache/invalidate", "/api/chat/conversations/route": "/api/chat/conversations", "/api/chat/messages/delete-after-timestamp/route": "/api/chat/messages/delete-after-timestamp", "/api/chat/messages/route": "/api/chat/messages", "/api/chat/messages/update-by-timestamp/route": "/api/chat/messages/update-by-timestamp", "/api/cleanup/pending-users/route": "/api/cleanup/pending-users", "/api/custom-configs/[configId]/default-chat-key/route": "/api/custom-configs/[configId]/default-chat-key", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "/api/custom-configs/[configId]/route": "/api/custom-configs/[configId]", "/api/custom-configs/[configId]/routing/route": "/api/custom-configs/[configId]/routing", "/api/custom-configs/route": "/api/custom-configs", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "/api/debug/checkout/route": "/api/debug/checkout", "/api/debug/clear-cache/route": "/api/debug/clear-cache", "/api/debug/supabase-test/route": "/api/debug/supabase-test", "/api/documents/[documentId]/route": "/api/documents/[documentId]", "/api/documents/list/route": "/api/documents/list", "/api/documents/search/route": "/api/documents/search", "/api/documents/upload/route": "/api/documents/upload", "/api/keys/[apiKeyId]/roles/[roleName]/route": "/api/keys/[apiKeyId]/roles/[roleName]", "/api/keys/[apiKeyId]/roles/route": "/api/keys/[apiKeyId]/roles", "/api/keys/[apiKeyId]/route": "/api/keys/[apiKeyId]", "/api/logs/route": "/api/logs", "/api/keys/route": "/api/keys", "/api/orchestration/process-step/route": "/api/orchestration/process-step", "/api/orchestration/start/route": "/api/orchestration/start", "/api/orchestration/status/[executionId]/route": "/api/orchestration/status/[executionId]", "/api/orchestration/stream/[executionId]/route": "/api/orchestration/stream/[executionId]", "/api/orchestration/synthesis-fallback/[executionId]/route": "/api/orchestration/synthesis-fallback/[executionId]", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "/api/orchestration/synthesis-stream-direct/[executionId]", "/api/orchestration/synthesis-stream/[executionId]/route": "/api/orchestration/synthesis-stream/[executionId]", "/api/playground/route": "/api/playground", "/api/providers/list-models/route": "/api/providers/list-models", "/api/pricing/tiers/route": "/api/pricing/tiers", "/api/quality-analytics/route": "/api/quality-analytics", "/api/quality-feedback/route": "/api/quality-feedback", "/api/stripe/customer-portal/route": "/api/stripe/customer-portal", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/stripe/subscription-status/route": "/api/stripe/subscription-status", "/api/stripe/webhooks/route": "/api/stripe/webhooks", "/api/system-status/route": "/api/system-status", "/api/training/jobs/route": "/api/training/jobs", "/api/training/jobs/upsert/route": "/api/training/jobs/upsert", "/api/user/custom-roles/[customRoleId]/route": "/api/user/custom-roles/[customRoleId]", "/api/user/custom-roles/route": "/api/user/custom-roles", "/auth/callback/route": "/auth/callback", "/api/v1/chat/completions/route": "/api/v1/chat/completions", "/favicon.ico/route": "/favicon.ico", "/_not-found/page": "/_not-found", "/add-keys/page": "/add-keys", "/analytics/page": "/analytics", "/auth/signin/page": "/auth/signin", "/auth/signup/page": "/auth/signup", "/auth/verify-email/page": "/auth/verify-email", "/checkout/page": "/checkout", "/dashboard/page": "/dashboard", "/debug-session/page": "/debug-session", "/logs/page": "/logs", "/my-models/[configId]/page": "/my-models/[configId]", "/my-models/page": "/my-models", "/page": "/", "/playground/page": "/playground", "/pricing/page": "/pricing", "/routing-setup/[configId]/page": "/routing-setup/[configId]", "/routing-setup/page": "/routing-setup", "/training/page": "/training", "/about/page": "/about", "/contact/page": "/contact", "/features/page": "/features", "/routing-strategies/page": "/routing-strategies"}